#!/usr/bin/env python3
"""
Verify the SERVER_LAYOUT configuration
"""

from ServerSetup import SERVER_LAYOUT, ROLES_DATA

def verify_layout():
    print("🔍 Verifying SERVER_LAYOUT Configuration")
    print("=" * 50)
    
    total_channels = 0
    expected_categories = [
        "✨┃WELCOME ZONE",
        "🎫┃SUPPORT CENTER", 
        "📦┃SERVICE HUB",
        "🪙┃BASTION ECONOMY",
        "🎖┃CLIENT TIERS",
        "🔒┃STAFF & ADMIN",
        "📑┃LOGS"
    ]
    
    print(f"📊 Expected categories: {len(expected_categories)}")
    print(f"📊 Actual categories: {len(SERVER_LAYOUT)}")
    
    if len(SERVER_LAYOUT) != len(expected_categories):
        print("❌ Category count mismatch!")
        return False
    
    for i, category in enumerate(SERVER_LAYOUT):
        category_name = category["name"]
        channels = category["channels"]
        
        print(f"\n📂 Category {i+1}: {category_name}")
        print(f"   Channels: {len(channels)}")
        
        if category_name != expected_categories[i]:
            print(f"❌ Expected: {expected_categories[i]}")
            print(f"❌ Got: {category_name}")
            return False
        
        for j, channel in enumerate(channels):
            channel_name = channel["name"]
            channel_type = channel.get("type", "text")
            print(f"   {j+1:2d}. {channel_name} ({channel_type})")
            total_channels += 1
    
    print(f"\n📊 Total channels: {total_channels}")
    print(f"📊 Expected: 30 channels")
    
    if total_channels == 30:
        print("✅ Channel count is correct!")
    else:
        print(f"❌ Expected 30 channels, got {total_channels}")
        return False
    
    print(f"\n🎭 Roles: {len(ROLES_DATA)}")
    for role in ROLES_DATA:
        print(f"   - {role['name']}")
    
    print("\n✅ All verification checks passed!")
    return True

if __name__ == "__main__":
    verify_layout()
