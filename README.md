# 🚀 Bastion Cloud™ Bot - Enhanced Version 2.1.0

An advanced Discord server management and setup bot with comprehensive error handling and enhanced features.

## 🔧 Fixes Applied

### Critical Bug Fixes
- ✅ **Fixed "Unknown Channel" Error**: Bot now preserves the setup channel during cleanup to prevent message editing failures
- ✅ **Enhanced Error Handling**: Improved command error handler with proper exception catching for deleted channels
- ✅ **Deprecated datetime.utcnow()**: Replaced with `datetime.now(timezone.utc)` for future compatibility
- ✅ **Better Cleanup Process**: Added delays and improved error handling during server cleanup
- ✅ **Role Deletion Order**: Roles are now deleted in proper hierarchy order to prevent permission errors

### New Features
- 🆕 **Permission Validation**: Bot validates required permissions before starting setup
- 🆕 **Environment Variable Support**: Supports `DISCORD_BOT_TOKEN` environment variable for secure token storage
- 🆕 **Enhanced Logging**: Better logging with more detailed information and error tracking
- 🆕 **Additional Commands**: Added utility commands for better bot management
- 🆕 **Graceful Error Recovery**: Improved rollback mechanism with detailed logging

## 📋 New Commands

| Command | Aliases | Description |
|---------|---------|-------------|
| `!setup_bastion` | `!setup`, `!install` | Complete server setup |
| `!check_permissions` | `!checkperms`, `!perms` | Verify bot permissions |
| `!ping` | - | Check bot latency |
| `!status` | - | Display bot status |
| `!version` | `!v` | Show version information |
| `!help` | `!h` | Display help menu |
| `!info` | `!about` | Bot information |

## 🛠️ Setup Instructions

### Prerequisites
- Python 3.13+ (recommended)
- discord.py library
- Administrator permissions in your Discord server

### Installation
1. **Install Dependencies**:
   ```bash
   pip install discord.py
   ```

2. **Configure Bot Token** (Choose one method):
   
   **Method 1: Environment Variable (Recommended)**
   ```bash
   # Windows
   set DISCORD_BOT_TOKEN=your_bot_token_here
   
   # Linux/Mac
   export DISCORD_BOT_TOKEN=your_bot_token_here
   ```
   
   **Method 2: Direct Code Edit**
   - Edit `ServerSetup.py` and replace the token in the `main()` function

3. **Run the Bot**:
   ```bash
   python ServerSetup.py
   ```

4. **Test the Bot**:
   ```bash
   python test_bot.py
   ```

## 🔐 Required Permissions

The bot requires the following Discord permissions:
- **Manage Channels** - Create/delete channels and categories
- **Manage Roles** - Create/delete roles
- **Send Messages** - Send setup progress messages
- **Embed Links** - Send rich embed messages
- **Read Message History** - Read existing messages

Use `!check_permissions` to verify all permissions are correctly set.

## 🚀 Usage

1. **Check Permissions**: `!check_permissions`
2. **Run Setup**: `!setup_bastion`
3. **Monitor Progress**: The bot will show real-time progress updates
4. **Verify Completion**: Check the success message and logs

## 📊 What Gets Created

### Roles (8 total)
- 🔴 Server Owner
- 🟠 Admin  
- 🟡 Staff
- 🔷 BastionBot
- ⚪ Tier I - Free Client
- 🔵 Tier II - Invite Client
- 🟣 Tier III - Booster Client
- 💚 Tier IV - Paid Client

### Categories & Channels (12 categories, 50+ channels)
- ✨ Welcome Zone (4 channels)
- 🎫 Support Center (4 channels)
- 📦 Service Hub (11 channels)
- 🪙 Bastion Economy (5 channels)
- 🎖 Client Tiers (4 channels)
- 🎓 Learning Center (3 channels)
- 📊 Status & Transparency (3 channels)
- 💡 Suggestions & Feedback (3 channels)
- 📰 Blog & Updates (2 channels)
- 🤝 Partner Network (2 channels)
- 🔒 Staff & Admin (4 channels)
- 📑 Log Center (5 channels)

## 🔄 Error Recovery

If setup fails:
1. **Automatic Rollback**: Bot automatically removes created items
2. **Detailed Logging**: Check `bastion_bot.log` for detailed error information
3. **Permission Check**: Run `!check_permissions` to verify bot permissions
4. **Retry Setup**: Fix issues and run `!setup_bastion` again

## 📝 Logging

The bot creates detailed logs in `bastion_bot.log` including:
- Setup progress and completion
- Error details and stack traces
- Permission validation results
- Server join/leave events
- Command usage statistics

## 🔧 Troubleshooting

### Common Issues

**"Unknown Channel" Error**
- ✅ **Fixed**: Bot now preserves setup channel during cleanup

**Permission Denied Errors**
- Run `!check_permissions` to verify bot permissions
- Ensure bot role is positioned high enough in role hierarchy

**Token Issues**
- Verify token is correct and bot is invited to server
- Check if `DISCORD_BOT_TOKEN` environment variable is set

**Rate Limiting**
- Bot includes automatic delays to prevent rate limiting
- If issues persist, wait a few minutes before retrying

## 📈 Version History

### v2.1.0 (Current)
- Fixed critical "Unknown Channel" error
- Added permission validation
- Enhanced error handling and logging
- Added utility commands
- Environment variable support
- Improved cleanup process

### v2.0.0 (Previous)
- Initial enhanced version
- Basic server setup functionality

## 🤝 Support

For issues or questions:
1. Check the logs in `bastion_bot.log`
2. Run `!check_permissions` to verify setup
3. Use `!help` for command information
4. Review this README for troubleshooting steps

---

**Bastion Cloud™ | Advanced Discord Server Management**
