import discord
from discord.ext import commands
import asyncio
import logging
from datetime import datetime
import sys
import os
from typing import List, Dict, Any

# Configure logging for human-readable console output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('bastion_bot.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# Bot configuration
intents = discord.Intents.default()
intents.members = True
intents.guilds = True
intents.message_content = True

bot = commands.Bot(
    command_prefix="!",
    intents=intents,
    help_command=None,  # We'll create a custom help command
    case_insensitive=True
)

# Enhanced roles data with hierarchy
ROLES_DATA = [
    {
        "name": "🔴 Server Owner",
        "color": discord.Color.dark_red(),
        "perms": discord.Permissions.all(),
        "position": 10
    },
    {
        "name": "🟠 Admin",
        "color": discord.Color.red(),
        "perms": discord.Permissions(administrator=True),
        "position": 9
    },
    {
        "name": "🟡 Staff",
        "color": discord.Color.orange(),
        "perms": discord.Permissions(
            manage_messages=True,
            kick_members=True,
            ban_members=True,
            manage_nicknames=True,
            mute_members=True
        ),
        "position": 8
    },
    {
        "name": "🔷 BastionBot",
        "color": discord.Color.teal(),
        "perms": discord.Permissions(
            manage_roles=True,
            manage_channels=True,
            send_messages=True,
            embed_links=True,
            manage_messages=True
        ),
        "position": 7
    },
    {
        "name": "⚪ Tier I - Free Client",
        "color": discord.Color.light_grey(),
        "perms": discord.Permissions(
            read_messages=True,
            send_messages=True,
            read_message_history=True
        ),
        "position": 4
    },
    {
        "name": "🔵 Tier II - Invite Client",
        "color": discord.Color.blue(),
        "perms": discord.Permissions(
            read_messages=True,
            send_messages=True,
            read_message_history=True,
            use_external_emojis=True
        ),
        "position": 5
    },
    {
        "name": "🟣 Tier III - Booster Client",
        "color": discord.Color.magenta(),
        "perms": discord.Permissions(
            read_messages=True,
            send_messages=True,
            read_message_history=True,
            use_external_emojis=True,
            embed_links=True
        ),
        "position": 6
    },
    {
        "name": "💚 Tier IV - Paid Client",
        "color": discord.Color.green(),
        "perms": discord.Permissions(
            read_messages=True,
            send_messages=True,
            read_message_history=True,
            connect=True,
            speak=True,
            attach_files=True,
            use_external_emojis=True,
            embed_links=True,
            add_reactions=True
        ),
        "position": 7
    },
]

# Enhanced server layout with permissions
SERVER_LAYOUT = [
    {
        "name": "✨┃WELCOME ZONE",
        "channels": [
            {"name": "📜┃rules", "type": "text", "topic": "Server rules and guidelines"},
            {"name": "📘┃faq", "type": "text", "topic": "Frequently asked questions"},
            {"name": "🎉┃announcements", "type": "text", "topic": "Important server announcements"},
            {"name": "👋┃introductions", "type": "text", "topic": "Introduce yourself to the community"}
        ]
    },
    {
        "name": "🎫┃SUPPORT CENTER",
        "channels": [
            {"name": "🎟┃open-ticket", "type": "text", "topic": "Open support tickets here"},
            {"name": "📂┃ticket-logs", "type": "text", "topic": "Ticket history and logs"},
            {"name": "🔧┃support-faq", "type": "text", "topic": "Common support questions"},
            {"name": "🧑‍💼┃contact-team", "type": "text", "topic": "Direct team contact"}
        ]
    },
    {
        "name": "📦┃SERVICE HUB",
        "channels": [
            {"name": "💬┃service-chat", "type": "text", "topic": "General service discussions"},
            {"name": "🧱┃minecraft-support", "type": "text", "topic": "Minecraft server support"},
            {"name": "⭐┃minecraft-feedback", "type": "text", "topic": "Minecraft service feedback"},
            {"name": "🤖┃bot-helpdesk", "type": "text", "topic": "Bot support and help"},
            {"name": "⭐┃bot-feedback", "type": "text", "topic": "Bot feedback and suggestions"},
            {"name": "🌐┃web-helpdesk", "type": "text", "topic": "Website support"},
            {"name": "⭐┃web-feedback", "type": "text", "topic": "Website feedback"},
            {"name": "💻┃vps-discussion", "type": "text", "topic": "VPS discussions"},
            {"name": "⭐┃vps-feedback", "type": "text", "topic": "VPS service feedback"},
            {"name": "🔊┃service-voice", "type": "voice", "topic": "Voice support channel"}
        ]
    },
    {
        "name": "🪙┃BASTION ECONOMY",
        "channels": [
            {"name": "💰┃earn-bastioncoins", "type": "text", "topic": "Earn BastionCoins here"},
            {"name": "🛍┃bastion-shop", "type": "text", "topic": "Purchase items with coins"},
            {"name": "🎁┃redeem-center", "type": "text", "topic": "Redeem rewards and codes"},
            {"name": "📥┃coin-requests", "type": "text", "topic": "Request coins for activities"},
            {"name": "📊┃leaderboard", "type": "text", "topic": "Economy leaderboards"}
        ]
    },
    {
        "name": "🎖┃CLIENT TIERS",
        "channels": [
            {"name": "🔓┃free-client-lounge", "type": "text", "topic": "Free tier client area"},
            {"name": "📨┃invite-client-lounge", "type": "text", "topic": "Invite tier client area"},
            {"name": "🎀┃booster-client-lounge", "type": "text", "topic": "Booster tier client area"},
            {"name": "💎┃paid-client-lounge", "type": "text", "topic": "Premium tier client area"}
        ]
    },
    {
        "name": "🎓┃LEARNING CENTER",
        "channels": [
            {"name": "📚┃getting-started", "type": "text", "topic": "New user guides"},
            {"name": "📁┃tutorial-vault", "type": "text", "topic": "Tutorials and guides"},
            {"name": "🛠┃dev-resources", "type": "text", "topic": "Developer resources"}
        ]
    },
    {
        "name": "📊┃STATUS & TRANSPARENCY",
        "channels": [
            {"name": "🟢┃service-status", "type": "text", "topic": "Real-time service status"},
            {"name": "🔁┃maintenance-log", "type": "text", "topic": "Maintenance schedules"},
            {"name": "🚨┃incident-report", "type": "text", "topic": "Incident reports"}
        ]
    },
    {
        "name": "💡┃SUGGESTIONS & FEEDBACK",
        "channels": [
            {"name": "🧠┃feature-suggestions", "type": "text", "topic": "Suggest new features"},
            {"name": "📥┃idea-voting", "type": "text", "topic": "Vote on community ideas"},
            {"name": "📊┃polls-n-surveys", "type": "text", "topic": "Community polls"}
        ]
    },
    {
        "name": "📰┃BLOG & UPDATES",
        "channels": [
            {"name": "📰┃dev-blogs", "type": "text", "topic": "Developer blog posts"},
            {"name": "📣┃bastion-broadcast", "type": "text", "topic": "Official broadcasts"}
        ]
    },
    {
        "name": "🤝┃PARTNER NETWORK",
        "channels": [
            {"name": "🔗┃partner-info", "type": "text", "topic": "Partner information"},
            {"name": "📢┃partner-promos", "type": "text", "topic": "Partner promotions"}
        ]
    },
    {
        "name": "🔒┃STAFF & ADMIN",
        "channels": [
            {"name": "📋┃staff-chat", "type": "text", "topic": "Staff discussion"},
            {"name": "📌┃mod-tools", "type": "text", "topic": "Moderation tools"},
            {"name": "💼┃admin-hub", "type": "text", "topic": "Admin discussions"},
            {"name": "🧪┃bot-testing", "type": "text", "topic": "Bot testing area"}
        ]
    },
    {
        "name": "📑┃LOG CENTER",
        "channels": [
            {"name": "📑┃join-logs", "type": "text", "topic": "Member join logs"},
            {"name": "📤┃leave-logs", "type": "text", "topic": "Member leave logs"},
            {"name": "🛠┃command-logs", "type": "text", "topic": "Command usage logs"},
            {"name": "🔐┃mod-logs", "type": "text", "topic": "Moderation action logs"},
            {"name": "🕵️┃audit-logs", "type": "text", "topic": "Server audit logs"}
        ]
    }
]

class BastionBot:
    def __init__(self):
        self.setup_in_progress = False
        self.created_roles = []
        self.created_categories = []
        
    async def cleanup_server(self, guild: discord.Guild) -> bool:
        """Clean up existing server structure"""
        try:
            logger.info(f"🧹 Starting server cleanup for: {guild.name}")
            
            # Delete all channels
            deleted_channels = 0
            for channel in guild.channels:
                try:
                    await channel.delete()
                    deleted_channels += 1
                except discord.HTTPException:
                    logger.warning(f"⚠️  Could not delete channel: {channel.name}")
            
            logger.info(f"✅ Deleted {deleted_channels} channels")
            
            # Delete roles (except @everyone and bot's role)
            deleted_roles = 0
            bot_member = guild.get_member(bot.user.id)
            bot_top_role = bot_member.top_role if bot_member else None
            
            for role in guild.roles:
                if role.name != "@everyone" and role != bot_top_role:
                    try:
                        await role.delete()
                        deleted_roles += 1
                    except discord.HTTPException:
                        logger.warning(f"⚠️  Could not delete role: {role.name}")
            
            logger.info(f"✅ Deleted {deleted_roles} roles")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
            return False
    
    async def create_roles(self, guild: discord.Guild) -> List[discord.Role]:
        """Create server roles with proper hierarchy"""
        created_roles = []
        
        try:
            logger.info("🎭 Creating server roles...")
            
            for role_data in sorted(ROLES_DATA, key=lambda x: x.get('position', 0)):
                try:
                    role = await guild.create_role(
                        name=role_data["name"],
                        color=role_data["color"],
                        permissions=role_data["perms"],
                        hoist=True,
                        mentionable=True
                    )
                    created_roles.append(role)
                    logger.info(f"✅ Created role: {role.name}")
                    
                    # Small delay to prevent rate limiting
                    await asyncio.sleep(0.5)
                    
                except discord.HTTPException as e:
                    logger.error(f"❌ Failed to create role {role_data['name']}: {e}")
            
            logger.info(f"🎭 Successfully created {len(created_roles)} roles")
            return created_roles
            
        except Exception as e:
            logger.error(f"❌ Error creating roles: {e}")
            return created_roles
    
    async def create_channels(self, guild: discord.Guild) -> List[discord.CategoryChannel]:
        """Create categories and channels"""
        created_categories = []
        
        try:
            logger.info("📁 Creating categories and channels...")
            
            for category_data in SERVER_LAYOUT:
                try:
                    # Create category
                    category = await guild.create_category(category_data["name"])
                    created_categories.append(category)
                    logger.info(f"📂 Created category: {category.name}")
                    
                    # Create channels in category
                    for channel_data in category_data["channels"]:
                        try:
                            channel_name = channel_data["name"]
                            channel_type = channel_data.get("type", "text")
                            topic = channel_data.get("topic", "")
                            
                            if channel_type == "voice":
                                channel = await category.create_voice_channel(
                                    name=channel_name,
                                    topic=topic
                                )
                            else:
                                channel = await category.create_text_channel(
                                    name=channel_name,
                                    topic=topic
                                )
                            
                            logger.info(f"  ✅ Created {channel_type} channel: {channel.name}")
                            
                        except discord.HTTPException as e:
                            logger.error(f"❌ Failed to create channel {channel_name}: {e}")
                    
                    # Small delay between categories
                    await asyncio.sleep(1)
                    
                except discord.HTTPException as e:
                    logger.error(f"❌ Failed to create category {category_data['name']}: {e}")
            
            logger.info(f"📁 Successfully created {len(created_categories)} categories")
            return created_categories
            
        except Exception as e:
            logger.error(f"❌ Error creating channels: {e}")
            return created_categories
    
    async def rollback_changes(self, guild: discord.Guild):
        """Rollback changes in case of failure"""
        logger.warning("🔄 Rolling back changes...")
        
        try:
            # Delete created roles
            for role in self.created_roles:
                try:
                    await role.delete()
                except:
                    pass
            
            # Delete created categories and their channels
            for category in self.created_categories:
                try:
                    for channel in category.channels:
                        await channel.delete()
                    await category.delete()
                except:
                    pass
            
            logger.info("✅ Rollback completed")
            
        except Exception as e:
            logger.error(f"❌ Error during rollback: {e}")

# Initialize bot manager
bastion_manager = BastionBot()

@bot.event
async def on_ready():
    """Bot ready event"""
    logger.info("=" * 50)
    logger.info("🚀 BASTION CLOUD™ BOT ONLINE")
    logger.info("=" * 50)
    logger.info(f"Bot Name: {bot.user.name}")
    logger.info(f"Bot ID: {bot.user.id}")
    logger.info(f"Connected to {len(bot.guilds)} server(s)")
    logger.info(f"Serving {sum(guild.member_count for guild in bot.guilds)} users")
    logger.info("=" * 50)
    
    # Set bot status
    await bot.change_presence(
        activity=discord.Activity(
            type=discord.ActivityType.watching,
            name="Bastion Cloud™ | !help"
        ),
        status=discord.Status.online
    )

@bot.event
async def on_command_error(ctx, error):
    """Handle command errors"""
    if isinstance(error, commands.MissingPermissions):
        await ctx.send("❌ You don't have permission to use this command!")
    elif isinstance(error, commands.CommandNotFound):
        await ctx.send("❓ Command not found. Use `!help` for available commands.")
    else:
        logger.error(f"Command error in {ctx.command}: {error}")
        await ctx.send(f"❌ An error occurred: {error}")

@bot.command(name='setup_bastion', aliases=['setup', 'install'])
@commands.has_permissions(administrator=True)
async def setup_bastion(ctx):
    """Setup the complete Bastion Cloud™ server structure"""
    
    if bastion_manager.setup_in_progress:
        await ctx.send("⚠️ Setup is already in progress! Please wait...")
        return
    
    bastion_manager.setup_in_progress = True
    guild = ctx.guild
    
    try:
        # Initial setup message
        embed = discord.Embed(
            title="🚀 Bastion Cloud™ Server Setup",
            description="Initializing complete server transformation...",
            color=discord.Color.blue(),
            timestamp=datetime.utcnow()
        )
        embed.add_field(name="Status", value="🔄 Starting...", inline=False)
        setup_msg = await ctx.send(embed=embed)
        
        logger.info(f"🚀 Starting Bastion setup for server: {guild.name} ({guild.id})")
        
        # Step 1: Cleanup
        embed.set_field_at(0, name="Status", value="🧹 Cleaning existing structure...", inline=False)
        await setup_msg.edit(embed=embed)
        
        if not await bastion_manager.cleanup_server(guild):
            raise Exception("Failed to cleanup server")
        
        # Step 2: Create roles
        embed.set_field_at(0, name="Status", value="🎭 Creating roles...", inline=False)
        await setup_msg.edit(embed=embed)
        
        bastion_manager.created_roles = await bastion_manager.create_roles(guild)
        
        # Step 3: Create channels
        embed.set_field_at(0, name="Status", value="📁 Creating categories and channels...", inline=False)
        await setup_msg.edit(embed=embed)
        
        bastion_manager.created_categories = await bastion_manager.create_channels(guild)
        
        # Step 4: Success
        embed.title = "✅ Bastion Cloud™ Setup Complete!"
        embed.description = "Your server has been successfully transformed!"
        embed.color = discord.Color.green()
        embed.set_field_at(0, name="📊 Summary", value=f"""
        **Roles Created:** {len(bastion_manager.created_roles)}
        **Categories Created:** {len(bastion_manager.created_categories)}
        **Total Channels:** {sum(len(cat.channels) for cat in bastion_manager.created_categories)}
        **Setup Time:** {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC
        """, inline=False)
        
        await setup_msg.edit(embed=embed)
        
        logger.info("🎉 Bastion Cloud™ setup completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")
        
        # Error message
        embed = discord.Embed(
            title="❌ Setup Failed",
            description=f"An error occurred during setup: {str(e)}",
            color=discord.Color.red(),
            timestamp=datetime.utcnow()
        )
        embed.add_field(name="Status", value="🔄 Rolling back changes...", inline=False)
        await setup_msg.edit(embed=embed)
        
        # Rollback
        await bastion_manager.rollback_changes(guild)
        
        embed.set_field_at(0, name="Status", value="✅ Rollback completed", inline=False)
        await setup_msg.edit(embed=embed)
        
    finally:
        bastion_manager.setup_in_progress = False
        bastion_manager.created_roles = []
        bastion_manager.created_categories = []

@bot.command(name='help', aliases=['h'])
async def help_command(ctx):
    """Display help information"""
    embed = discord.Embed(
        title="🤖 Bastion Cloud™ Bot Commands",
        description="Here are the available commands:",
        color=discord.Color.blue(),
        timestamp=datetime.utcnow()
    )
    
    embed.add_field(
        name="🚀 Setup Commands",
        value="`!setup_bastion` - Complete server setup\n`!setup` - Alias for setup_bastion",
        inline=False
    )
    
    embed.add_field(
        name="ℹ️ Information",
        value="`!help` - Show this help menu\n`!info` - Bot information",
        inline=False
    )
    
    embed.set_footer(text="Bastion Cloud™ | Advanced Discord Server Management")
    await ctx.send(embed=embed)

@bot.command(name='info', aliases=['about'])
async def info_command(ctx):
    """Display bot information"""
    embed = discord.Embed(
        title="🤖 Bastion Cloud™ Bot",
        description="Advanced Discord server management and setup bot",
        color=discord.Color.blue(),
        timestamp=datetime.utcnow()
    )
    
    embed.add_field(name="Version", value="2.0.0", inline=True)
    embed.add_field(name="Servers", value=str(len(bot.guilds)), inline=True)
    embed.add_field(name="Users", value=str(sum(guild.member_count for guild in bot.guilds)), inline=True)
    
    embed.set_footer(text="Bastion Cloud™ | Premium Discord Solutions")
    await ctx.send(embed=embed)

def main():
    """Main function to run the bot"""
    # ⚠️ TEMPORARY: Direct token in code (NOT RECOMMENDED FOR PRODUCTION)
    # Replace 'YOUR_BOT_TOKEN_HERE' with your actual bot token
    DISCORD_TOKEN = "MTM3MjU5MjUwOTYxNTg2NjAxNw.GA7zxo.ywp_5N9H4IG4PQLfBX5uH7GeI3wa31cs0n7dPs"
    
    # Check if token was replaced
    if DISCORD_TOKEN == "YOUR_BOT_TOKEN_HERE":
        logger.error("❌ Please replace 'YOUR_BOT_TOKEN_HERE' with your actual bot token!")
        logger.info("💡 Find your token at: https://discord.com/developers/applications")
        logger.info("   Go to your bot → Bot section → Copy Token")
        sys.exit(1)
    
    try:
        logger.info("🔄 Starting Bastion Cloud™ Bot...")
        logger.warning("⚠️  WARNING: Token is hardcoded in source code!")
        logger.warning("⚠️  This is only for temporary testing - use environment variables for production!")
        bot.run(DISCORD_TOKEN)
    except discord.LoginFailure:
        logger.error("❌ Invalid bot token provided!")
        logger.info("💡 Make sure you copied the complete token from Discord Developer Portal")
    except Exception as e:
        logger.error(f"❌ Failed to start bot: {e}")

if __name__ == "__main__":
    main()