import discord
from discord.ext import commands
import asyncio
import logging
from datetime import datetime, timezone
import sys
import os
from typing import List, Dict, Any, Optional

# Configure logging for human-readable console output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('bastion_bot.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# Bot configuration
intents = discord.Intents.default()
intents.members = True
intents.guilds = True
intents.message_content = True

bot = commands.Bot(
    command_prefix="!",
    intents=intents,
    help_command=None,  # We'll create a custom help command
    case_insensitive=True
)

# Roles data based on your exact specifications
ROLES_DATA = [
    {
        "name": "Admin",
        "color": discord.Color.from_rgb(255, 0, 0),  # #FF0000
        "perms": discord.Permissions(
            administrator=True,
            manage_guild=True,
            manage_roles=True
        ),
        "position": 8,
        "description": "Complete control of the server, users, economy & settings."
    },
    {
        "name": "Staff",
        "color": discord.Color.from_rgb(255, 136, 0),  # #FF8800
        "perms": discord.Permissions(
            manage_messages=True,
            kick_members=True,
            mute_members=True
        ),
        "position": 7,
        "description": "Support team handling tickets, moderation, and users."
    },
    {
        "name": "BastionBot",
        "color": discord.Color.from_rgb(0, 255, 255),  # #00FFFF
        "perms": discord.Permissions(
            manage_roles=True,
            send_messages=True,
            read_messages=True,
            manage_channels=True,
            embed_links=True,
            read_message_history=True
        ),
        "position": 6,
        "description": "Your server bot for automation and BastionCoin handling."
    },
    {
        "name": "Tier I - Free Client",
        "color": discord.Color.from_rgb(128, 128, 128),  # #808080
        "perms": discord.Permissions(
            read_messages=True,
            send_messages=True,
            read_message_history=True
        ),
        "position": 5,
        "description": "Basic access users who use free or trial services."
    },
    {
        "name": "Tier II - Invite Client",
        "color": discord.Color.from_rgb(51, 153, 255),  # #3399FF
        "perms": discord.Permissions(
            read_messages=True,
            send_messages=True,
            read_message_history=True
        ),
        "position": 4,
        "description": "Gained access by inviting new members."
    },
    {
        "name": "Tier III - Booster Client",
        "color": discord.Color.from_rgb(255, 102, 255),  # #FF66FF
        "perms": discord.Permissions(
            read_messages=True,
            send_messages=True,
            read_message_history=True
        ),
        "position": 3,
        "description": "Users boosting the server, with premium-like perks."
    },
    {
        "name": "Tier IV - Paid Client",
        "color": discord.Color.from_rgb(51, 204, 51),  # #33CC33
        "perms": discord.Permissions(
            read_messages=True,
            send_messages=True,
            read_message_history=True,
            connect=True,
            speak=True,
            attach_files=True
        ),
        "position": 2,
        "description": "Verified clients with premium access to services."
    },
]

# Server layout based on your exact specifications
SERVER_LAYOUT = [
    {
        "name": "✨┃WELCOME ZONE",
        "description": "General public onboarding area",
        "channels": [
            {"name": "📜┃rules", "type": "text", "topic": "Server rules and behavior guidelines.", "access": "everyone"},
            {"name": "📘┃faq", "type": "text", "topic": "Server system and economy-related FAQs.", "access": "everyone"},
            {"name": "🎉┃announcements", "type": "text", "topic": "Server updates and new features.", "access": "everyone"},
            {"name": "👋┃introductions", "type": "text", "topic": "Meet and greet other members!", "access": "everyone"}
        ]
    },
    {
        "name": "🎫┃SUPPORT CENTER",
        "description": "Support and ticket system",
        "channels": [
            {"name": "🎟┃open-ticket", "type": "text", "topic": "Create a ticket for any support.", "access": "everyone"},
            {"name": "📂┃ticket-logs", "type": "text", "topic": "Archive of closed tickets.", "access": "staff"},
            {"name": "🧑‍💼┃contact-team", "type": "text", "topic": "Contact admins for serious inquiries.", "access": "everyone"}
        ]
    },
    {
        "name": "📦┃SERVICE HUB",
        "description": "Unified discussion zone for all services",
        "channels": [
            {"name": "💬┃service-chat", "type": "text", "topic": "General chat about any Bastion service.", "access": "tier_clients"},
            {"name": "🧱┃minecraft-chat", "type": "text", "topic": "Minecraft hosting-specific questions.", "access": "tier_clients"},
            {"name": "⭐┃minecraft-feedback", "type": "text", "topic": "Feedback from Minecraft clients.", "access": "tier_clients"},
            {"name": "🤖┃bot-chat", "type": "text", "topic": "Discord bot support and configs.", "access": "tier_clients"},
            {"name": "⭐┃bot-feedback", "type": "text", "topic": "Bot service feedback.", "access": "tier_clients"},
            {"name": "🌐┃web-chat", "type": "text", "topic": "Website hosting help and design talk.", "access": "tier_clients"},
            {"name": "⭐┃web-feedback", "type": "text", "topic": "Web hosting feedback.", "access": "tier_clients"},
            {"name": "💻┃vps-chat", "type": "text", "topic": "VPS deployment, OS, and container discussion.", "access": "tier_clients"},
            {"name": "⭐┃vps-feedback", "type": "text", "topic": "VPS service experience and issues.", "access": "tier_clients"},
            {"name": "🎧┃service-vc", "type": "voice", "topic": "Support or chat in voice.", "access": "tier_clients"}
        ]
    },
    {
        "name": "🪙┃BASTION ECONOMY",
        "description": "BastionCoin system and economy marketplace",
        "channels": [
            {"name": "💰┃earn-bastioncoins", "type": "text", "topic": "Learn how to earn BastionCoins.", "access": "everyone"},
            {"name": "🛍┃bastion-shop", "type": "text", "topic": "Redeem coins for services.", "access": "everyone"},
            {"name": "📥┃coin-requests", "type": "text", "topic": "Request your earned coin transactions.", "access": "everyone"},
            {"name": "📊┃leaderboard", "type": "text", "topic": "Top coin earners of the week/month.", "access": "everyone"}
        ]
    },
    {
        "name": "🎖┃CLIENT TIERS",
        "description": "Role-separated private chats",
        "channels": [
            {"name": "🔓┃free-clients", "type": "text", "topic": "Free client lounge.", "access": "tier_i"},
            {"name": "📨┃invite-clients", "type": "text", "topic": "For users who earned access via invites.", "access": "tier_ii"},
            {"name": "🎀┃booster-clients", "type": "text", "topic": "Premium booster-only chat.", "access": "tier_iii"},
            {"name": "💎┃paid-clients", "type": "text", "topic": "Paid premium client zone.", "access": "tier_iv"}
        ]
    },
    {
        "name": "🔒┃STAFF & ADMIN",
        "description": "Internal channels",
        "channels": [
            {"name": "�┃staff-chat", "type": "text", "topic": "General staff discussion.", "access": "staff"},
            {"name": "�┃mod-tools", "type": "text", "topic": "Warnings, kicks, bot tools.", "access": "staff"},
            {"name": "�┃admin-hub", "type": "text", "topic": "Strategy, economy, and automation decisions.", "access": "admin"},
            {"name": "🧪┃bot-testing", "type": "text", "topic": "Test and debug BastionBot features.", "access": "admin"}
        ]
    },
    {
        "name": "📑┃LOGS",
        "description": "Automated log channels",
        "channels": [
            {"name": "�┃join-logs", "type": "text", "topic": "User joins.", "access": "staff"},
            {"name": "�┃leave-logs", "type": "text", "topic": "User leaves and kicks.", "access": "staff"},
            {"name": "🛠┃command-logs", "type": "text", "topic": "Bot command executions.", "access": "staff"},
            {"name": "🔐┃mod-logs", "type": "text", "topic": "Actions like warn, mute, ban.", "access": "staff"},
            {"name": "🕵️┃audit-logs", "type": "text", "topic": "Admin-level server changes.", "access": "admin"}
        ]
    }
]

class BastionBot:
    def __init__(self):
        self.setup_in_progress = False
        self.created_roles = []
        self.created_categories = []
        self.setup_channel = None  # Store the channel where setup was initiated
        
    async def cleanup_server(self, guild: discord.Guild, preserve_channel: Optional[discord.TextChannel] = None) -> bool:
        """Clean up existing server structure while preserving the setup channel"""
        try:
            logger.info(f"🧹 Starting server cleanup for: {guild.name}")

            # Delete all channels except the preserve_channel
            deleted_channels = 0
            channels_to_delete = []

            # Collect channels to delete (excluding the preserve channel)
            for channel in guild.channels:
                if preserve_channel and channel.id == preserve_channel.id:
                    continue  # Skip the channel where setup was initiated
                channels_to_delete.append(channel)

            # Delete collected channels
            for channel in channels_to_delete:
                try:
                    await channel.delete()
                    deleted_channels += 1
                    await asyncio.sleep(0.2)  # Small delay to prevent rate limiting
                except discord.HTTPException as e:
                    logger.warning(f"⚠️  Could not delete channel: {channel.name} - {e}")

            logger.info(f"✅ Deleted {deleted_channels} channels")

            # Delete roles (except @everyone and bot's role)
            deleted_roles = 0
            bot_member = guild.get_member(bot.user.id)
            bot_top_role = bot_member.top_role if bot_member else None

            roles_to_delete = []
            for role in guild.roles:
                if role.name != "@everyone" and role != bot_top_role and not role.managed:
                    roles_to_delete.append(role)

            # Delete roles in reverse order (highest position first)
            for role in sorted(roles_to_delete, key=lambda r: r.position, reverse=True):
                try:
                    await role.delete()
                    deleted_roles += 1
                    await asyncio.sleep(0.2)  # Small delay to prevent rate limiting
                except discord.HTTPException as e:
                    logger.warning(f"⚠️  Could not delete role: {role.name} - {e}")

            logger.info(f"✅ Deleted {deleted_roles} roles")
            return True

        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
            return False
    
    async def create_roles(self, guild: discord.Guild) -> List[discord.Role]:
        """Create server roles with proper hierarchy"""
        created_roles = []
        
        try:
            logger.info("🎭 Creating server roles...")
            
            for role_data in sorted(ROLES_DATA, key=lambda x: x.get('position', 0)):
                try:
                    role = await guild.create_role(
                        name=role_data["name"],
                        color=role_data["color"],
                        permissions=role_data["perms"],
                        hoist=True,
                        mentionable=True
                    )
                    created_roles.append(role)
                    logger.info(f"✅ Created role: {role.name}")
                    
                    # Small delay to prevent rate limiting
                    await asyncio.sleep(0.5)
                    
                except discord.HTTPException as e:
                    logger.error(f"❌ Failed to create role {role_data['name']}: {e}")
            
            logger.info(f"🎭 Successfully created {len(created_roles)} roles")
            return created_roles
            
        except Exception as e:
            logger.error(f"❌ Error creating roles: {e}")
            return created_roles
    
    async def create_channels(self, guild: discord.Guild) -> List[discord.CategoryChannel]:
        """Create categories and channels"""
        created_categories = []
        
        try:
            logger.info("📁 Creating categories and channels...")
            
            for category_data in SERVER_LAYOUT:
                try:
                    # Create category
                    category = await guild.create_category(category_data["name"])
                    created_categories.append(category)
                    logger.info(f"📂 Created category: {category.name}")
                    
                    # Create channels in category
                    for channel_data in category_data["channels"]:
                        try:
                            channel_name = channel_data["name"]
                            channel_type = channel_data.get("type", "text")
                            topic = channel_data.get("topic", "")
                            
                            if channel_type == "voice":
                                channel = await category.create_voice_channel(
                                    name=channel_name
                                )
                            else:
                                channel = await category.create_text_channel(
                                    name=channel_name,
                                    topic=topic
                                )
                            
                            logger.info(f"  ✅ Created {channel_type} channel: {channel.name}")
                            
                        except discord.HTTPException as e:
                            logger.error(f"❌ Failed to create channel {channel_name}: {e}")
                    
                    # Small delay between categories
                    await asyncio.sleep(1)
                    
                except discord.HTTPException as e:
                    logger.error(f"❌ Failed to create category {category_data['name']}: {e}")
            
            logger.info(f"📁 Successfully created {len(created_categories)} categories")
            return created_categories
            
        except Exception as e:
            logger.error(f"❌ Error creating channels: {e}")
            return created_categories
    
    async def rollback_changes(self, guild: discord.Guild):
        """Rollback changes in case of failure"""
        logger.warning("🔄 Rolling back changes...")

        try:
            rollback_count = 0

            # Delete created roles
            for role in self.created_roles:
                try:
                    await role.delete()
                    rollback_count += 1
                    logger.info(f"🗑️ Rolled back role: {role.name}")
                    await asyncio.sleep(0.2)
                except discord.HTTPException as e:
                    logger.warning(f"⚠️ Could not rollback role {role.name}: {e}")
                except Exception as e:
                    logger.error(f"❌ Unexpected error rolling back role {role.name}: {e}")

            # Delete created categories and their channels
            for category in self.created_categories:
                try:
                    channel_count = len(category.channels)
                    for channel in category.channels:
                        try:
                            await channel.delete()
                            await asyncio.sleep(0.2)
                        except discord.HTTPException as e:
                            logger.warning(f"⚠️ Could not delete channel {channel.name}: {e}")

                    await category.delete()
                    rollback_count += 1 + channel_count
                    logger.info(f"🗑️ Rolled back category: {category.name} and {channel_count} channels")
                    await asyncio.sleep(0.2)
                except discord.HTTPException as e:
                    logger.warning(f"⚠️ Could not rollback category {category.name}: {e}")
                except Exception as e:
                    logger.error(f"❌ Unexpected error rolling back category {category.name}: {e}")

            logger.info(f"✅ Rollback completed - {rollback_count} items removed")

        except Exception as e:
            logger.error(f"❌ Critical error during rollback: {e}")

    async def validate_permissions(self, guild: discord.Guild) -> bool:
        """Validate that the bot has necessary permissions"""
        try:
            bot_member = guild.get_member(bot.user.id)
            if not bot_member:
                logger.error("❌ Bot member not found in guild")
                return False

            required_perms = [
                'manage_channels',
                'manage_roles',
                'send_messages',
                'embed_links',
                'read_message_history'
            ]

            missing_perms = []
            for perm in required_perms:
                if not getattr(bot_member.guild_permissions, perm, False):
                    missing_perms.append(perm)

            if missing_perms:
                logger.error(f"❌ Missing permissions: {', '.join(missing_perms)}")
                return False

            logger.info("✅ All required permissions verified")
            return True

        except Exception as e:
            logger.error(f"❌ Error validating permissions: {e}")
            return False

# Initialize bot manager
bastion_manager = BastionBot()

@bot.event
async def on_ready():
    """Bot ready event"""
    logger.info("=" * 50)
    logger.info("🚀 BASTION CLOUD™ BOT ONLINE")
    logger.info("=" * 50)
    logger.info(f"Bot Name: {bot.user.name}")
    logger.info(f"Bot ID: {bot.user.id}")
    logger.info(f"Connected to {len(bot.guilds)} server(s)")
    logger.info(f"Serving {sum(guild.member_count for guild in bot.guilds)} users")
    logger.info("=" * 50)

    # Set bot status
    await bot.change_presence(
        activity=discord.Activity(
            type=discord.ActivityType.watching,
            name="Bastion Cloud™ | !help"
        ),
        status=discord.Status.online
    )

@bot.event
async def on_disconnect():
    """Bot disconnect event"""
    logger.warning("🔌 Bot disconnected from Discord")

@bot.event
async def on_resumed():
    """Bot resumed event"""
    logger.info("🔄 Bot connection resumed")

@bot.event
async def on_guild_join(guild):
    """Bot joined a new guild"""
    logger.info(f"🎉 Joined new server: {guild.name} ({guild.id}) with {guild.member_count} members")

@bot.event
async def on_guild_remove(guild):
    """Bot removed from a guild"""
    logger.info(f"👋 Left server: {guild.name} ({guild.id})")

@bot.event
async def on_command_error(ctx, error):
    """Handle command errors with better error handling"""
    try:
        if isinstance(error, commands.MissingPermissions):
            await ctx.send("❌ You don't have permission to use this command!")
        elif isinstance(error, commands.CommandNotFound):
            await ctx.send("❓ Command not found. Use `!help` for available commands.")
        elif isinstance(error, discord.NotFound):
            # Channel was deleted, log the error but don't try to send a message
            logger.error(f"Command error in {ctx.command if ctx.command else 'unknown'}: Channel not found - {error}")
        else:
            logger.error(f"Command error in {ctx.command if ctx.command else 'unknown'}: {error}")
            try:
                await ctx.send(f"❌ An error occurred: {error}")
            except discord.NotFound:
                # If we can't send to the channel, just log it
                logger.error(f"Could not send error message to channel - channel may have been deleted")
    except Exception as e:
        logger.error(f"Error in error handler: {e}")

@bot.command(name='setup_bastion', aliases=['setup', 'install'])
@commands.has_permissions(administrator=True)
async def setup_bastion(ctx):
    """Setup the complete Bastion Cloud™ server structure"""

    if bastion_manager.setup_in_progress:
        await ctx.send("⚠️ Setup is already in progress! Please wait...")
        return

    bastion_manager.setup_in_progress = True
    bastion_manager.setup_channel = ctx.channel  # Store the setup channel
    guild = ctx.guild
    setup_msg = None

    try:
        # Initial setup message
        embed = discord.Embed(
            title="🚀 Bastion Cloud™ Server Setup",
            description="Initializing complete server transformation...",
            color=discord.Color.blue(),
            timestamp=datetime.now(timezone.utc)
        )
        embed.add_field(name="Status", value="🔄 Starting...", inline=False)
        setup_msg = await ctx.send(embed=embed)

        logger.info(f"🚀 Starting Bastion setup for server: {guild.name} ({guild.id})")

        # Step 0: Validate permissions
        embed.set_field_at(0, name="Status", value="🔍 Validating permissions...", inline=False)
        try:
            await setup_msg.edit(embed=embed)
        except discord.NotFound:
            logger.warning("Setup message was deleted during validation")
            setup_msg = None

        if not await bastion_manager.validate_permissions(guild):
            raise Exception("Insufficient permissions. Bot needs: Manage Channels, Manage Roles, Send Messages, Embed Links, Read Message History")

        # Step 1: Cleanup (preserve the setup channel)
        embed.set_field_at(0, name="Status", value="🧹 Cleaning existing structure...", inline=False)
        try:
            await setup_msg.edit(embed=embed)
        except discord.NotFound:
            logger.warning("Setup message was deleted during cleanup")
            setup_msg = None

        if not await bastion_manager.cleanup_server(guild, preserve_channel=ctx.channel):
            raise Exception("Failed to cleanup server")

        # Step 2: Create roles
        embed.set_field_at(0, name="Status", value="🎭 Creating roles...", inline=False)
        if setup_msg:
            try:
                await setup_msg.edit(embed=embed)
            except discord.NotFound:
                setup_msg = None

        bastion_manager.created_roles = await bastion_manager.create_roles(guild)

        # Step 3: Create channels
        embed.set_field_at(0, name="Status", value="📁 Creating categories and channels...", inline=False)
        if setup_msg:
            try:
                await setup_msg.edit(embed=embed)
            except discord.NotFound:
                setup_msg = None

        bastion_manager.created_categories = await bastion_manager.create_channels(guild)

        # Step 4: Success
        embed.title = "✅ Bastion Cloud™ Setup Complete!"
        embed.description = "Your server has been successfully transformed!"
        embed.color = discord.Color.green()
        embed.set_field_at(0, name="📊 Summary", value=f"""
        **Roles Created:** {len(bastion_manager.created_roles)}
        **Categories Created:** {len(bastion_manager.created_categories)}
        **Total Channels:** {sum(len(cat.channels) for cat in bastion_manager.created_categories)}
        **Setup Time:** {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')} UTC
        """, inline=False)

        if setup_msg:
            try:
                await setup_msg.edit(embed=embed)
            except discord.NotFound:
                # If original message was deleted, try to send a new one
                try:
                    await ctx.send(embed=embed)
                except:
                    pass
        else:
            # Try to send completion message to the setup channel if it still exists
            try:
                await ctx.send(embed=embed)
            except:
                pass

        logger.info("🎉 Bastion Cloud™ setup completed successfully!")

    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")

        # Error message
        embed = discord.Embed(
            title="❌ Setup Failed",
            description=f"An error occurred during setup: {str(e)}",
            color=discord.Color.red(),
            timestamp=datetime.now(timezone.utc)
        )
        embed.add_field(name="Status", value="🔄 Rolling back changes...", inline=False)

        if setup_msg:
            try:
                await setup_msg.edit(embed=embed)
            except discord.NotFound:
                setup_msg = None

        # Rollback
        await bastion_manager.rollback_changes(guild)

        embed.set_field_at(0, name="Status", value="✅ Rollback completed", inline=False)
        if setup_msg:
            try:
                await setup_msg.edit(embed=embed)
            except discord.NotFound:
                try:
                    await ctx.send(embed=embed)
                except:
                    pass
        else:
            try:
                await ctx.send(embed=embed)
            except:
                pass

    finally:
        bastion_manager.setup_in_progress = False
        bastion_manager.created_roles = []
        bastion_manager.created_categories = []
        bastion_manager.setup_channel = None

@bot.command(name='help', aliases=['h'])
async def help_command(ctx):
    """Display help information"""
    embed = discord.Embed(
        title="🤖 Bastion Cloud™ Bot Commands",
        description="Here are the available commands:",
        color=discord.Color.blue(),
        timestamp=datetime.now(timezone.utc)
    )

    embed.add_field(
        name="🚀 Setup Commands",
        value="`!setup_bastion` - Complete server setup\n`!setup` - Alias for setup_bastion\n`!install` - Alias for setup_bastion",
        inline=False
    )

    embed.add_field(
        name="ℹ️ Information",
        value="`!help` - Show this help menu\n`!info` - Bot information\n`!status` - Bot status",
        inline=False
    )

    embed.add_field(
        name="🛠️ Utility Commands",
        value="`!ping` - Check bot latency\n`!version` - Bot version info\n`!check_permissions` - Verify bot permissions",
        inline=False
    )

    embed.set_footer(text="Bastion Cloud™ | Advanced Discord Server Management")
    await ctx.send(embed=embed)

@bot.command(name='info', aliases=['about'])
async def info_command(ctx):
    """Display bot information"""
    embed = discord.Embed(
        title="🤖 Bastion Cloud™ Bot",
        description="Advanced Discord server management and setup bot",
        color=discord.Color.blue(),
        timestamp=datetime.now(timezone.utc)
    )

    embed.add_field(name="Version", value="2.1.0", inline=True)
    embed.add_field(name="Servers", value=str(len(bot.guilds)), inline=True)
    embed.add_field(name="Users", value=str(sum(guild.member_count for guild in bot.guilds)), inline=True)
    embed.add_field(name="Latency", value=f"{round(bot.latency * 1000)}ms", inline=True)
    embed.add_field(name="Python", value="3.13+", inline=True)
    embed.add_field(name="Discord.py", value=discord.__version__, inline=True)

    embed.set_footer(text="Bastion Cloud™ | Premium Discord Solutions")
    await ctx.send(embed=embed)

@bot.command(name='ping')
async def ping_command(ctx):
    """Check bot latency"""
    latency = round(bot.latency * 1000)
    embed = discord.Embed(
        title="🏓 Pong!",
        description=f"Bot latency: **{latency}ms**",
        color=discord.Color.green() if latency < 100 else discord.Color.orange() if latency < 200 else discord.Color.red(),
        timestamp=datetime.now(timezone.utc)
    )
    await ctx.send(embed=embed)

@bot.command(name='status')
async def status_command(ctx):
    """Display bot status"""
    embed = discord.Embed(
        title="📊 Bot Status",
        color=discord.Color.green(),
        timestamp=datetime.now(timezone.utc)
    )

    embed.add_field(name="🟢 Online", value="Bot is operational", inline=False)
    embed.add_field(name="📈 Uptime", value="Since last restart", inline=True)
    embed.add_field(name="🔧 Setup Status", value="Ready" if not bastion_manager.setup_in_progress else "In Progress", inline=True)
    embed.add_field(name="🌐 Latency", value=f"{round(bot.latency * 1000)}ms", inline=True)

    await ctx.send(embed=embed)

@bot.command(name='version', aliases=['v'])
async def version_command(ctx):
    """Display version information"""
    embed = discord.Embed(
        title="📋 Version Information",
        color=discord.Color.blue(),
        timestamp=datetime.now(timezone.utc)
    )

    embed.add_field(name="Bot Version", value="2.1.0", inline=True)
    embed.add_field(name="Discord.py", value=discord.__version__, inline=True)
    embed.add_field(name="Python", value="3.13+", inline=True)
    embed.add_field(name="Features", value="✅ Enhanced Error Handling\n✅ Channel Preservation\n✅ Improved Cleanup\n✅ Better Logging", inline=False)

    await ctx.send(embed=embed)

@bot.command(name='check_permissions', aliases=['checkperms', 'perms'])
@commands.has_permissions(administrator=True)
async def check_permissions(ctx):
    """Check if the bot has all required permissions"""
    guild = ctx.guild

    embed = discord.Embed(
        title="🔍 Permission Check",
        color=discord.Color.blue(),
        timestamp=datetime.now(timezone.utc)
    )

    if await bastion_manager.validate_permissions(guild):
        embed.color = discord.Color.green()
        embed.add_field(name="✅ Status", value="All required permissions are available!", inline=False)
        embed.add_field(name="🚀 Ready", value="You can run `!setup_bastion` to begin setup", inline=False)
    else:
        embed.color = discord.Color.red()
        embed.add_field(name="❌ Status", value="Missing required permissions!", inline=False)
        embed.add_field(name="🔧 Required", value="• Manage Channels\n• Manage Roles\n• Send Messages\n• Embed Links\n• Read Message History", inline=False)
        embed.add_field(name="💡 Solution", value="Please ensure the bot role has these permissions and is positioned high enough in the role hierarchy", inline=False)

    await ctx.send(embed=embed)

def main():
    """Main function to run the bot"""
    # Try to get token from environment variable first
    DISCORD_TOKEN = os.getenv('DISCORD_BOT_TOKEN')

    # Fallback to hardcoded token (NOT RECOMMENDED FOR PRODUCTION)
    if not DISCORD_TOKEN:
        DISCORD_TOKEN = "MTM3MjU5MjUwOTYxNTg2NjAxNw.GA7zxo.ywp_5N9H4IG4PQLfBX5uH7GeI3wa31cs0n7dPs"
        logger.warning("⚠️  WARNING: Using hardcoded token!")
        logger.warning("⚠️  For production, set DISCORD_BOT_TOKEN environment variable!")
    else:
        logger.info("✅ Using token from environment variable")

    # Check if token was replaced
    if DISCORD_TOKEN == "YOUR_BOT_TOKEN_HERE" or not DISCORD_TOKEN:
        logger.error("❌ No valid bot token found!")
        logger.info("💡 Either:")
        logger.info("   1. Set DISCORD_BOT_TOKEN environment variable, OR")
        logger.info("   2. Replace 'YOUR_BOT_TOKEN_HERE' with your actual bot token")
        logger.info("💡 Find your token at: https://discord.com/developers/applications")
        logger.info("   Go to your bot → Bot section → Copy Token")
        sys.exit(1)

    try:
        logger.info("🔄 Starting Bastion Cloud™ Bot...")
        logger.info(f"🤖 Bot Version: 2.1.0")
        logger.info(f"🐍 Python Version: {sys.version}")
        logger.info(f"📚 Discord.py Version: {discord.__version__}")
        logger.info("=" * 50)

        bot.run(DISCORD_TOKEN)
    except discord.LoginFailure:
        logger.error("❌ Invalid bot token provided!")
        logger.info("💡 Make sure you copied the complete token from Discord Developer Portal")
    except KeyboardInterrupt:
        logger.info("🛑 Bot shutdown requested by user")
    except Exception as e:
        logger.error(f"❌ Failed to start bot: {e}")
        logger.error(f"❌ Error type: {type(e).__name__}")
        import traceback
        logger.error(f"❌ Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()