# Fixed SERVER_LAYOUT with proper emojis
SERVER_LAYOUT = [
    {
        "name": "✨┃WELCOME ZONE",
        "description": "General public onboarding area",
        "channels": [
            {"name": "📜┃rules", "type": "text", "topic": "Server rules and behavior guidelines.", "access": "everyone"},
            {"name": "📘┃faq", "type": "text", "topic": "Server system and economy-related FAQs.", "access": "everyone"},
            {"name": "🎉┃announcements", "type": "text", "topic": "Server updates and new features.", "access": "everyone"},
            {"name": "👋┃introductions", "type": "text", "topic": "Meet and greet other members!", "access": "everyone"}
        ]
    },
    {
        "name": "🎫┃SUPPORT CENTER",
        "description": "Support and ticket system",
        "channels": [
            {"name": "🎟┃open-ticket", "type": "text", "topic": "Create a ticket for any support.", "access": "everyone"},
            {"name": "📂┃ticket-logs", "type": "text", "topic": "Archive of closed tickets.", "access": "staff"},
            {"name": "🧑‍💼┃contact-team", "type": "text", "topic": "Contact admins for serious inquiries.", "access": "everyone"}
        ]
    },
    {
        "name": "📦┃SERVICE HUB",
        "description": "Unified discussion zone for all services",
        "channels": [
            {"name": "💬┃service-chat", "type": "text", "topic": "General chat about any Bastion service.", "access": "tier_clients"},
            {"name": "🧱┃minecraft-chat", "type": "text", "topic": "Minecraft hosting-specific questions.", "access": "tier_clients"},
            {"name": "⭐┃minecraft-feedback", "type": "text", "topic": "Feedback from Minecraft clients.", "access": "tier_clients"},
            {"name": "🤖┃bot-chat", "type": "text", "topic": "Discord bot support and configs.", "access": "tier_clients"},
            {"name": "⭐┃bot-feedback", "type": "text", "topic": "Bot service feedback.", "access": "tier_clients"},
            {"name": "🌐┃web-chat", "type": "text", "topic": "Website hosting help and design talk.", "access": "tier_clients"},
            {"name": "⭐┃web-feedback", "type": "text", "topic": "Web hosting feedback.", "access": "tier_clients"},
            {"name": "💻┃vps-chat", "type": "text", "topic": "VPS deployment, OS, and container discussion.", "access": "tier_clients"},
            {"name": "⭐┃vps-feedback", "type": "text", "topic": "VPS service experience and issues.", "access": "tier_clients"},
            {"name": "🎧┃service-vc", "type": "voice", "topic": "Support or chat in voice.", "access": "tier_clients"}
        ]
    },
    {
        "name": "🪙┃BASTION ECONOMY",
        "description": "BastionCoin system and economy marketplace",
        "channels": [
            {"name": "💰┃earn-bastioncoins", "type": "text", "topic": "Learn how to earn BastionCoins.", "access": "everyone"},
            {"name": "🛍┃bastion-shop", "type": "text", "topic": "Redeem coins for services.", "access": "everyone"},
            {"name": "📥┃coin-requests", "type": "text", "topic": "Request your earned coin transactions.", "access": "everyone"},
            {"name": "📊┃leaderboard", "type": "text", "topic": "Top coin earners of the week/month.", "access": "everyone"}
        ]
    },
    {
        "name": "🎖┃CLIENT TIERS",
        "description": "Role-separated private chats",
        "channels": [
            {"name": "🔓┃free-clients", "type": "text", "topic": "Free client lounge.", "access": "tier_i"},
            {"name": "📨┃invite-clients", "type": "text", "topic": "For users who earned access via invites.", "access": "tier_ii"},
            {"name": "🎀┃booster-clients", "type": "text", "topic": "Premium booster-only chat.", "access": "tier_iii"},
            {"name": "💎┃paid-clients", "type": "text", "topic": "Paid premium client zone.", "access": "tier_iv"}
        ]
    },
    {
        "name": "🔒┃STAFF & ADMIN",
        "description": "Internal channels",
        "channels": [
            {"name": "📋┃staff-chat", "type": "text", "topic": "General staff discussion.", "access": "staff"},
            {"name": "📌┃mod-tools", "type": "text", "topic": "Warnings, kicks, bot tools.", "access": "staff"},
            {"name": "💼┃admin-hub", "type": "text", "topic": "Strategy, economy, and automation decisions.", "access": "admin"},
            {"name": "🧪┃bot-testing", "type": "text", "topic": "Test and debug BastionBot features.", "access": "admin"}
        ]
    },
    {
        "name": "📑┃LOGS",
        "description": "Automated log channels",
        "channels": [
            {"name": "📑┃join-logs", "type": "text", "topic": "User joins.", "access": "staff"},
            {"name": "📤┃leave-logs", "type": "text", "topic": "User leaves and kicks.", "access": "staff"},
            {"name": "🛠┃command-logs", "type": "text", "topic": "Bot command executions.", "access": "staff"},
            {"name": "🔐┃mod-logs", "type": "text", "topic": "Actions like warn, mute, ban.", "access": "staff"},
            {"name": "🕵️┃audit-logs", "type": "text", "topic": "Admin-level server changes.", "access": "admin"}
        ]
    }
]

# Count channels
total = sum(len(cat["channels"]) for cat in SERVER_LAYOUT)
print(f"Total channels: {total}")
