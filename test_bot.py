#!/usr/bin/env python3
"""
Test script for Bastion Cloud™ Bot
This script performs basic validation of the bot code without running it.
"""

import sys
import os
import importlib.util

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    
    required_modules = [
        'discord',
        'asyncio',
        'logging',
        'datetime',
        'typing'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ Missing modules: {', '.join(missing_modules)}")
        print("💡 Install with: pip install discord.py")
        return False
    
    print("✅ All imports successful!")
    return True

def test_syntax():
    """Test if the bot script has valid syntax"""
    print("\n🔍 Testing syntax...")
    
    try:
        spec = importlib.util.spec_from_file_location("serversetup", "ServerSetup.py")
        if spec is None:
            print("❌ Could not load ServerSetup.py")
            return False
        
        module = importlib.util.module_from_spec(spec)
        # Don't execute, just compile to check syntax
        with open("ServerSetup.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, "ServerSetup.py", "exec")
        print("✅ Syntax validation passed!")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        print(f"   Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Error loading script: {e}")
        return False

def test_configuration():
    """Test bot configuration"""
    print("\n🔍 Testing configuration...")
    
    # Check if token is set in environment
    token = os.getenv('DISCORD_BOT_TOKEN')
    if token:
        print("✅ DISCORD_BOT_TOKEN environment variable is set")
    else:
        print("⚠️  DISCORD_BOT_TOKEN environment variable not set")
        print("   Bot will use hardcoded token (not recommended for production)")
    
    # Check log file permissions
    try:
        with open('bastion_bot.log', 'a', encoding='utf-8') as f:
            f.write(f"# Test log entry - {sys.version}\n")
        print("✅ Log file is writable")
    except Exception as e:
        print(f"⚠️  Log file issue: {e}")
    
    return True

def main():
    """Run all tests"""
    print("🚀 Bastion Cloud™ Bot - Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_syntax,
        test_configuration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Bot should run correctly.")
        print("💡 Run with: python ServerSetup.py")
    else:
        print("❌ Some tests failed. Please fix issues before running the bot.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
