2025-07-30 15:32:23 | INFO | 🔄 Starting Bastion Cloud™ Bot...
2025-07-30 15:32:23 | WARNING | ⚠️  WARNING: Token is hardcoded in source code!
2025-07-30 15:32:23 | WARNING | ⚠️  This is only for temporary testing - use environment variables for production!
2025-07-30 15:32:23 | INFO | logging in using static token
2025-07-30 15:32:25 | INFO | Shard ID None has connected to Gateway (Session ID: 62f100d64e279077d976fd6ca62b16c9).
2025-07-30 15:32:27 | INFO | ==================================================
2025-07-30 15:32:27 | INFO | 🚀 BASTION CLOUD™ BOT ONLINE
2025-07-30 15:32:27 | INFO | ==================================================
2025-07-30 15:32:27 | INFO | Bot Name: ✦  TestingBot4 ✦
2025-07-30 15:32:27 | INFO | Bot ID: 1372592509615866017
2025-07-30 15:32:27 | INFO | Connected to 1 server(s)
2025-07-30 15:32:27 | INFO | Serving 4 users
2025-07-30 15:32:27 | INFO | ==================================================
2025-07-30 15:33:08 | INFO | 🚀 Starting Bastion setup for server: 🇧🇦🇸🇹🇮🇴🇳 🇨🇱🇴🇺🇩™ | 🇮🇹 🇸🇴🇱🇺🇹🇮🇴🇳🇸 (1399988367361441913)
2025-07-30 15:33:08 | INFO | 🧹 Starting server cleanup for: 🇧🇦🇸🇹🇮🇴🇳 🇨🇱🇴🇺🇩™ | 🇮🇹 🇸🇴🇱🇺🇹🇮🇴🇳🇸
2025-07-30 15:33:10 | WARNING | ⚠️  Could not delete channel: ◟🥀︲rules﹒
2025-07-30 15:33:31 | WARNING | ⚠️  Could not delete channel: ◟🥀︲info﹒
2025-07-30 15:33:32 | INFO | ✅ Deleted 34 channels
2025-07-30 15:33:33 | WARNING | ⚠️  Could not delete role: R.O.T.I
2025-07-30 15:33:34 | INFO | ✅ Deleted 2 roles
2025-07-30 15:33:34 | ERROR | ❌ Setup failed: 404 Not Found (error code: 10003): Unknown Channel
2025-07-30 15:33:35 | ERROR | Command error in setup_bastion: Command raised an exception: NotFound: 404 Not Found (error code: 10003): Unknown Channel
2025-07-30 15:33:35 | ERROR | Ignoring exception in on_command_error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "f:\New folder\ServerSetup.py", line 420, in on_command_error
    await ctx.send(f"❌ An error occurred: {error}")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\ext\commands\context.py", line 1024, in send
    return await super().send(
           ^^^^^^^^^^^^^^^^^^^
    ...<15 lines>...
    )  # type: ignore # The overloads don't support Optional but the implementation does
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\abc.py", line 1561, in send
    data = await state.http.send_message(channel.id, params=params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\http.py", line 741, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10003): Unknown Channel
# Test log entry - 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
# Test log entry - 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-07-30 15:42:52 | WARNING | ⚠️  WARNING: Using hardcoded token!
2025-07-30 15:42:52 | WARNING | ⚠️  For production, set DISCORD_BOT_TOKEN environment variable!
2025-07-30 15:42:52 | INFO | 🔄 Starting Bastion Cloud™ Bot...
2025-07-30 15:42:52 | INFO | 🤖 Bot Version: 2.1.0
2025-07-30 15:42:52 | INFO | 🐍 Python Version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-07-30 15:42:52 | INFO | 📚 Discord.py Version: 2.3.2
2025-07-30 15:42:52 | INFO | ==================================================
2025-07-30 15:42:52 | INFO | logging in using static token
2025-07-30 15:42:54 | INFO | Shard ID None has connected to Gateway (Session ID: 3687433e8e5ae4f7a5004e328c0738d9).
2025-07-30 15:42:56 | INFO | ==================================================
2025-07-30 15:42:56 | INFO | 🚀 BASTION CLOUD™ BOT ONLINE
2025-07-30 15:42:56 | INFO | ==================================================
2025-07-30 15:42:56 | INFO | Bot Name: ✦  TestingBot4 ✦
2025-07-30 15:42:56 | INFO | Bot ID: 1372592509615866017
2025-07-30 15:42:56 | INFO | Connected to 1 server(s)
2025-07-30 15:42:56 | INFO | Serving 4 users
2025-07-30 15:42:56 | INFO | ==================================================
2025-07-30 15:43:10 | INFO | 🚀 Starting Bastion setup for server: 🇧🇦🇸🇹🇮🇴🇳 🇨🇱🇴🇺🇩™ | 🇮🇹 🇸🇴🇱🇺🇹🇮🇴🇳🇸 (1399988367361441913)
2025-07-30 15:43:11 | INFO | ✅ All required permissions verified
2025-07-30 15:43:11 | INFO | 🧹 Starting server cleanup for: 🇧🇦🇸🇹🇮🇴🇳 🇨🇱🇴🇺🇩™ | 🇮🇹 🇸🇴🇱🇺🇹🇮🇴🇳🇸
2025-07-30 15:43:11 | INFO | ✅ Deleted 0 channels
2025-07-30 15:43:11 | INFO | ✅ Deleted 0 roles
2025-07-30 15:43:12 | INFO | 🎭 Creating server roles...
2025-07-30 15:43:12 | INFO | ✅ Created role: ⚪ Tier I - Free Client
2025-07-30 15:43:13 | INFO | ✅ Created role: 🔵 Tier II - Invite Client
2025-07-30 15:43:14 | INFO | ✅ Created role: 🟣 Tier III - Booster Client
2025-07-30 15:43:15 | INFO | ✅ Created role: 🔷 BastionBot
2025-07-30 15:43:16 | INFO | ✅ Created role: 💚 Tier IV - Paid Client
2025-07-30 15:43:17 | INFO | ✅ Created role: 🟡 Staff
2025-07-30 15:43:18 | INFO | ✅ Created role: 🟠 Admin
2025-07-30 15:43:19 | INFO | ✅ Created role: 🔴 Server Owner
2025-07-30 15:43:20 | INFO | 🎭 Successfully created 8 roles
2025-07-30 15:43:20 | INFO | 📁 Creating categories and channels...
2025-07-30 15:43:21 | INFO | 📂 Created category: ✨┃WELCOME ZONE
2025-07-30 15:43:21 | INFO |   ✅ Created text channel: 📜┃rules
2025-07-30 15:43:22 | INFO |   ✅ Created text channel: 📘┃faq
2025-07-30 15:43:22 | INFO |   ✅ Created text channel: 🎉┃announcements
2025-07-30 15:43:23 | INFO |   ✅ Created text channel: 👋┃introductions
2025-07-30 15:43:24 | INFO | 📂 Created category: 🎫┃SUPPORT CENTER
2025-07-30 15:43:25 | INFO |   ✅ Created text channel: 🎟┃open-ticket
2025-07-30 15:43:25 | INFO |   ✅ Created text channel: 📂┃ticket-logs
2025-07-30 15:43:26 | INFO |   ✅ Created text channel: 🔧┃support-faq
2025-07-30 15:43:26 | INFO |   ✅ Created text channel: 🧑‍💼┃contact-team
2025-07-30 15:43:28 | INFO | 📂 Created category: 📦┃SERVICE HUB
2025-07-30 15:43:28 | INFO |   ✅ Created text channel: 💬┃service-chat
2025-07-30 15:43:29 | INFO |   ✅ Created text channel: 🧱┃minecraft-support
2025-07-30 15:43:29 | INFO |   ✅ Created text channel: ⭐┃minecraft-feedback
2025-07-30 15:43:29 | INFO |   ✅ Created text channel: 🤖┃bot-helpdesk
2025-07-30 15:43:30 | INFO |   ✅ Created text channel: ⭐┃bot-feedback
2025-07-30 15:43:30 | INFO |   ✅ Created text channel: 🌐┃web-helpdesk
2025-07-30 15:43:31 | INFO |   ✅ Created text channel: ⭐┃web-feedback
2025-07-30 15:43:32 | INFO |   ✅ Created text channel: 💻┃vps-discussion
2025-07-30 15:43:33 | INFO |   ✅ Created text channel: ⭐┃vps-feedback
2025-07-30 15:43:33 | ERROR | ❌ Error creating channels: Guild.create_voice_channel() got an unexpected keyword argument 'topic'
2025-07-30 15:43:33 | INFO | 🎉 Bastion Cloud™ setup completed successfully!
# Test log entry - 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
