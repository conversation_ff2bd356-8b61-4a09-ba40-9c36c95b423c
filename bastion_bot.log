2025-07-30 15:32:23 | INFO | 🔄 Starting Bastion Cloud™ Bot...
2025-07-30 15:32:23 | WARNING | ⚠️  WARNING: Token is hardcoded in source code!
2025-07-30 15:32:23 | WARNING | ⚠️  This is only for temporary testing - use environment variables for production!
2025-07-30 15:32:23 | INFO | logging in using static token
2025-07-30 15:32:25 | INFO | Shard ID None has connected to Gateway (Session ID: 62f100d64e279077d976fd6ca62b16c9).
2025-07-30 15:32:27 | INFO | ==================================================
2025-07-30 15:32:27 | INFO | 🚀 BASTION CLOUD™ BOT ONLINE
2025-07-30 15:32:27 | INFO | ==================================================
2025-07-30 15:32:27 | INFO | Bot Name: ✦  TestingBot4 ✦
2025-07-30 15:32:27 | INFO | Bot ID: 1372592509615866017
2025-07-30 15:32:27 | INFO | Connected to 1 server(s)
2025-07-30 15:32:27 | INFO | Serving 4 users
2025-07-30 15:32:27 | INFO | ==================================================
2025-07-30 15:33:08 | INFO | 🚀 Starting Bastion setup for server: 🇧🇦🇸🇹🇮🇴🇳 🇨🇱🇴🇺🇩™ | 🇮🇹 🇸🇴🇱🇺🇹🇮🇴🇳🇸 (1399988367361441913)
2025-07-30 15:33:08 | INFO | 🧹 Starting server cleanup for: 🇧🇦🇸🇹🇮🇴🇳 🇨🇱🇴🇺🇩™ | 🇮🇹 🇸🇴🇱🇺🇹🇮🇴🇳🇸
2025-07-30 15:33:10 | WARNING | ⚠️  Could not delete channel: ◟🥀︲rules﹒
2025-07-30 15:33:31 | WARNING | ⚠️  Could not delete channel: ◟🥀︲info﹒
2025-07-30 15:33:32 | INFO | ✅ Deleted 34 channels
2025-07-30 15:33:33 | WARNING | ⚠️  Could not delete role: R.O.T.I
2025-07-30 15:33:34 | INFO | ✅ Deleted 2 roles
2025-07-30 15:33:34 | ERROR | ❌ Setup failed: 404 Not Found (error code: 10003): Unknown Channel
2025-07-30 15:33:35 | ERROR | Command error in setup_bastion: Command raised an exception: NotFound: 404 Not Found (error code: 10003): Unknown Channel
2025-07-30 15:33:35 | ERROR | Ignoring exception in on_command_error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "f:\New folder\ServerSetup.py", line 420, in on_command_error
    await ctx.send(f"❌ An error occurred: {error}")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\ext\commands\context.py", line 1024, in send
    return await super().send(
           ^^^^^^^^^^^^^^^^^^^
    ...<15 lines>...
    )  # type: ignore # The overloads don't support Optional but the implementation does
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\abc.py", line 1561, in send
    data = await state.http.send_message(channel.id, params=params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\http.py", line 741, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10003): Unknown Channel
